<template>
  <div class="task-container">
    <!-- 基本信息 -->
    <div style="background-color: white; padding: 15px; margin-bottom: 15px;box-shadow: 0 2px 4px 0 rgba(0,0,0,.12), 0 0 6px 0 rgba(0,0,0,.04);">
      <el-form ref="form" label-width="140px" size="small" clearable>
        <el-form-item label="名称">
          <el-input v-model="dataFrom.name" style="width: 600px;" clearable />
        </el-form-item>
        <el-form-item label="用途">
          <el-input v-model="dataFrom.describeInfo" style="width: 600px;" clearable />
        </el-form-item>
        <el-form-item label="文档">
          <el-input v-model="dataFrom.helpUrl" style="width: 600px;" clearable />
        </el-form-item>
        <el-form-item label="属性">
          <el-table
            v-if="dataFrom.attrList.length > 0"
            size="mini"
            :data="dataFrom.attrList"
            border
            style="width: 600px; margin-bottom: 10px;"
          >
            <el-table-column
              prop="name"
              label="名称"
            />
            <el-table-column
              prop="value"
              label="值"
            />
            <el-table-column
              label="操作"
              align="center"
              width="60"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="dataFrom.editPower"
                  size="mini"
                  type="text"
                  @click.native.prevent="deleteRow(scope.$index, dataFrom.attrList)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button v-if="dataFrom.editPower" size="mini" @click="addAttr">添加</el-button>
        </el-form-item>
        <el-form-item label="允许申请不超卖资源">
          <el-switch v-model="dataFrom.notOversell" disabled /> <el-link href="https://xbp.jd.com/4/apply/22603" target="_blank" size="mini" type="primary">申请流程</el-link>
        </el-form-item>
        <el-form-item v-if="currentTopoId" label="负责人">
          <el-input v-model="dataFrom.erp" disabled style="width: 300px; margin-right: 15px;" />
          <el-button @click="handleChangeOwner">转移</el-button>
          <el-button @click="handleAllowUser">授权</el-button>
        </el-form-item>
        <el-form-item v-if="dataFrom.editPower">
          <el-button type="primary" @click="handleSave">保存</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 拓扑属性操作 -->
    <el-dialog
      title="添加拓扑属性"
      :visible.sync="attrVisible"
      width="400px"
    >
      <div>
        名称: <el-input v-model.trim="tempAttr.name" style="margin-bottom: 15px;" clearable />
        值: <el-input v-model.trim="tempAttr.value" clearable />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmAttr">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 权限交接 -->
    <el-dialog
      title="转移负责人"
      :visible.sync="ownerVisible"
      width="400px"
    >
      <div>
        请输入有效的ERP: <el-input v-model.trim="changeOwner" style="margin-bottom: 15px;" clearable />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmChangeOwner">确认交接</el-button>
      </span>
    </el-dialog>

    <!-- 授权用户 -->
    <el-dialog
      title="编辑授权用户"
      :visible.sync="allowVisible"
      width="600px"
    >
      <div style="margin-bottom: 10px;">
        <el-tag
          v-for="tag in dataFrom.powerUserList"
          :key="tag.id"
          closable
          @close="handleCloseAllowUser(tag)"
        >
          {{ tag.username }}
        </el-tag>
      </div>

      <el-input v-model.trim="allowUserErp" clearable />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmAllowOwner">保存授权用户</el-button>
      </span>
    </el-dialog>

    <!-- 节点属性 -->
    <el-dialog
      title="添加节点属性"
      :visible.sync="nodeAttrVisible"
      width="400px"
    >
      <div>
        名称: <el-input v-model.trim="tempNodeAttr.name" style="margin-bottom: 15px;" clearable />
        值: <el-input v-model.trim="tempNodeAttr.value" clearable />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmNodeAttr">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 节点编辑与新增 -->
    <el-drawer
      size="32%"
      :title="nodeTitle"
      :visible.sync="nodeDrawer"
    >
      <div style="padding: 10px">
        <el-divider><i class="el-icon-mobile-phone">节点属性</i></el-divider>
        <el-form label-width="80px" style="margin-bottom: 15px;">
          <el-form-item label="节点名">
            <el-input v-model.trim="nodeForm.name" disabled clearable style="width: 400px" />
          </el-form-item>
          <el-form-item label="模块">
            <el-select v-model="nodeForm.moduleId" filterable placeholder="请选择模块" style="width: 400px;" clearable @change="onChangeModule">
              <el-option
                v-for="item in moduleList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="节点类型">
            <span v-if="nodeForm.type === 2" style="font-size: 13px;">
              <strong>工具节点</strong>
            </span>
            <el-radio-group v-else v-model="nodeForm.type" @change="onChangeNodeType">
              <template>
                <el-radio :label="0">base</el-radio>
                <el-radio :label="1">test</el-radio>
              </template>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="依赖节点">
            <el-select v-model="nodeForm.dependencyNodeIdList" multiple placeholder="请选择依赖节点" style="width: 400px;" clearable>
              <el-option
                v-for="item in dependencyNodeItems"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="节点属性">
            <el-table
              v-if="nodeForm.attrList.length > 0"
              size="mini"
              :data="nodeForm.attrList"
              border
              style="width: 400px; margin-bottom: 10px;"
            >
              <el-table-column
                prop="name"
                label="名称"
              />
              <el-table-column
                prop="value"
                label="值"
              />
              <el-table-column
                label="操作"
                align="center"
                width="60"
              >
                <template slot-scope="scope">
                  <el-button
                    v-if="scope.row.name !== 'group'"
                    size="mini"
                    type="text"
                    @click.native.prevent="deleteRow(scope.$index, nodeForm.attrList)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button size="mini" @click="addNodeAttr">添加节点属性</el-button>
          </el-form-item>

          <el-form-item v-if="dataFrom.editPower">
            <el-button size="mini" type="primary" @click="handleSaveNode">保存节点</el-button>
            <el-button v-if="nodeForm.id" size="mini" type="danger" @click="handleDelNode">删除节点</el-button>
          </el-form-item>
        </el-form>

        <!-- 模块属性 -->
        <template v-if="nodeForm.moduleInfo">
          <el-divider><i class="el-icon-mobile-phone">模块属性</i></el-divider>
          <el-descriptions class="margin-top" :column="1" size="mini" border>
            <el-descriptions-item>
              <template slot="label">
                模块名称
              </template>
              {{ nodeForm.moduleInfo.name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                类型
              </template>
              {{ getModuleTypeLabel(nodeForm.moduleInfo.type) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                Platform
              </template>
              {{ nodeForm.moduleInfo.platform }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                Limitation
              </template>
              {{ getModuleLimitationLabel(nodeForm.moduleInfo.limitation) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                CPU
              </template>
              {{ nodeForm.moduleInfo.cpu }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                内存
              </template>
              {{ nodeForm.moduleInfo.mem }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                最小磁盘
              </template>
              {{ nodeForm.moduleInfo.minDisk }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                GIT地址
              </template>
              {{ nodeForm.moduleInfo.git }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                Build脚本
              </template>
              {{ nodeForm.moduleInfo.buildScript }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                Update脚本
              </template>
              {{ nodeForm.moduleInfo.updateScript }}
            </el-descriptions-item>
          </el-descriptions>
        </template>
      </div>
    </el-drawer>

    <template v-if="currentTopoId">
      <!-- 报告配置 -->
      <report-setting :topology-id="currentTopoId" />
      <!-- 拓扑图 -->
      <div style="margin-bottom: 5px;">
        <el-button v-if="dataFrom.editPower" @click="openNode">添加节点</el-button>
        <el-button @click="refreshGraphLayout">刷新布局</el-button>
        <el-button @click="fitGraphToView">适应视图</el-button>
        <el-button type="warning" @click="testVirtualRoot">测试虚拟根节点</el-button>
      </div>

      <!-- 拓扑图 -->
      <div class="img-container">
        <RelationGraph ref="seeksRelationGraph" :options="graphOptions" :on-node-click="onNodeClick" />
      </div>
    </template>
  </div>
</template>

<script>
import RelationGraph from 'relation-graph'
import API from '@/api'
import { MODULE_TYPES, MODULE_MACHINE_LIMIT_TYPES } from '@/settings/module'
import ReportSetting from '@/views/topology/component/ReportSetting.vue'

export default {
  name: 'TopologyDetail',
  components: { RelationGraph, ReportSetting },
  data() {
    return {
      changeOwner: null, // 交接人
      allowUserErp: null, // 授权用户
      nodeAttrVisible: false, // 编辑属性弹窗
      attrVisible: false, // 编辑属性弹窗
      ownerVisible: false, // 权限交接弹窗
      allowVisible: false, // 授权弹窗
      currentTopoId: null, // 当前页面拓扑ID
      nodeDrawer: false,
      nodeTitle: '新增节点',
      dependencyNodeItems: [], // 当前可选的依赖节点，需要排除自身
      nodeForm: { // 当前节点
        id: null,
        name: null, // 节点名
        moduleId: null,
        dependencyNodeIdList: [],
        attrList: [],
        type: null, // 节点类型
        moduleInfo: null
      },
      dataFrom: { // 当前拓扑详情
        name: null,
        describeInfo: null,
        isAutoPlacement: false,
        helpUrl: null,
        attr: null, // 属性
        notOversell: false,
        erp: null,
        attrList: [],
        nodeLines: [],
        nodes: [],
        powerUserList: [], // 有权限的用户
        editPower: false // 编辑权限
      },
      tempAttr: { // 临时拓扑属性
        name: null,
        value: null
      },
      tempNodeAttr: { // 临时拓扑属性
        name: null,
        value: null
      },
      moduleList: [], // 模块列表
      nodeMenuPanelPosition: { x: 0, y: 0 },
      drawerTitle: '编辑节点',
      graphOptions: {
        defaultNodeBorderWidth: 0,
        defaultNodeColor: 'rgba(238, 178, 94, 1)',
        defaultLineWidth: 1,
        placeSingleNode: true,
        disableZoom: false, // 启用数据缩放
        allowSwitchLineShape: false, // 禁用线条
        allowSwitchJunctionPoint: false, // 禁用
        defaultJunctionPoint: 'border',
        allowAutoLayoutIfSupport: true, // 启用自动布局
        zoomToFitWhenRefresh: true, // 刷新时自动适应
        // 树形布局配置
        layout: {
          label: '树形布局',
          layoutName: 'tree',
          centerOffset_x: 0,
          centerOffset_y: 50,
          distance_coefficient: 1.5, // 增加节点间距系数
          from: 'top', // 从顶部开始布局
          levelDistance: 150, // 增加层级间距
          min_per_width: 200, // 最小节点宽度
          max_per_width: 400, // 最大节点宽度
          min_per_height: 100, // 最小节点高度
          max_per_height: 150, // 最大节点高度
          maxLayoutTimes: 1000, // 增加最大布局次数
          layoutDirection: 'v', // 垂直布局
          layouter: 'tree' // 树形布局器
        }
      }
    }
  },
  created() {
    this.initGraphData()
  },
  methods: {
    handleChangeOwner() {
      this.changeOwner = null
      this.ownerVisible = true
    },
    handleAllowUser() {
      // 授权弹窗
      this.allowUserErp = null
      this.allowVisible = true
    },
    initGraphData() {
      const id = this.$route.params.id
      console.log('当前ID', id)
      if (id && id > 0) {
        this.currentTopoId = id
        this.getDetail()
      } else {
        this.currentTopoId = null
        this.dataFrom.editPower = true
      }
    },
    deleteRow(index, rows) {
      // 删除属性，动态
      rows.splice(index, 1)
    },
    addNodeAttr() {
      // 添加节点属性
      this.tempNodeAttr = { // 清空原来的默认是
        name: null,
        value: null
      }
      this.nodeAttrVisible = true
    },
    addAttr() {
      // 添加属性
      this.tempAttr = { // 清空原来的默认是
        name: null,
        value: null
      }
      this.attrVisible = true
    },
    async confirmChangeOwner() {
      // 交接负责人
      const params = { id: this.currentTopoId, owner: this.changeOwner }
      console.log('交接', params)
      await API.changeTopologyOwner(params)
      this.ownerVisible = false
      await this.getDetail()
      this.$message.success('转移负责人成功！')
    },
    async confirmAllowOwner() {
      // 添加授权人
      const params = { newErp: this.allowUserErp, erpList: this.dataFrom.powerUserList || [], id: this.currentTopoId }
      console.log('授权用户', params)
      await API.saveTopologyAllowUser(params)
      this.allowVisible = false
      this.$message.success('保存成功！')

      await this.getDetail()
    },
    async handleDelNode() {
      // 删除节点
      const params = {
        topologyId: this.currentTopoId,
        nodeId: this.nodeForm.id
      }

      console.log('删除节点', params)
      await API.delNodeById(params)
      this.nodeDrawer = false
      this.$message.success('删除节点成功！')

      await this.getDetail()
    },
    confirmAttr() {
      // 确认添加属性
      const params = {
        name: this.tempAttr.name,
        value: this.tempAttr.value
      }
      if (params.name === '' || params.name === undefined || params.name === null) {
        this.$message.error('名称不合法！')
        return
      }

      if (params.value === '' || params.value === undefined || params.value === null) {
        this.$message.error('值不合法！')
        return
      }
      this.dataFrom.attrList.push(params)
      this.$message.success('添加成功! 保存生效！')
      this.attrVisible = false
    },
    confirmNodeAttr() {
      // 确认添加属性
      const params = {
        name: this.tempNodeAttr.name,
        value: this.tempNodeAttr.value
      }
      if (params.name === '' || params.name === undefined || params.name === null) {
        this.$message.error('名称不合法！')
        return
      }

      if (params.value === '' || params.value === undefined || params.value === null) {
        this.$message.error('值不合法！')
        return
      }
      this.nodeForm.attrList.push(params)
      this.$message.success('添加成功! 保存生效！')
      this.nodeAttrVisible = false
    },
    async getDetail() {
      // 获取详情
      const response = await API.fetchTopolopyDetailById({ id: this.currentTopoId }) // 调用 API 获取任务列表
      console.log('=======', response)

      if (response.code === 0) {
        this.dataFrom = response.data
      }

      setTimeout(() => {
        this.showSeeksGraph()
      }, 500)
    },
    onChangeModule(moduleId) {
      // 编辑节点，切换模块时
      console.log('切换模块', moduleId)
      const module = this.moduleList.find(item => item.id === moduleId) // 找模块基础
      console.log('当前模块: ', module)

      // 从已有的里面去统计已经存在的个数
      const count = this.dataFrom.nodes.filter(item => item.moduleId === moduleId).length // 统计
      console.log('统计', count)
      // 1代表工具节点，0代表应用节点
      if (module.type === 1) {
        // 工具节点
        this.nodeForm.type = 2 // 工具节点固定是2
        this.nodeForm.name = module.name + '_' + count
      } else if (module.type === 0) {
        this.nodeForm.type = 0 // 代表应用节点下的base
        this.nodeForm.name = module.name + '_base_' + count
      }

      this.nodeForm.moduleId = moduleId
      this.nodeForm.moduleInfo = module
      if (this.nodeForm.id) {
        // 编辑
      } else {
        // 新增
        this.nodeForm.attrList = [{ name: 'group', value: count }] // TODO 加上模块自身的属性
      }

      // 新增默认加上group和模块自身属性
      // 编辑则不变

      console.log('this.nodeForm.attrList', this.nodeForm.attrList)
    },
    onChangeNodeType() {
      console.log('切换类型')
      const count = this.dataFrom.nodes.filter(item => item.moduleId === this.nodeForm.moduleId).length // 统计
      console.log('统计', count)
      if (this.nodeForm.type === 0) {
        this.nodeForm.name = this.nodeForm.moduleInfo.name + '_base_' + count
      } else if (this.nodeForm.type === 1) {
        this.nodeForm.name = this.nodeForm.moduleInfo.name + '_test_' + count
      }
    },
    async handleSaveNode() {
      const dependencyNodes = []
      const dependencyNodeIdList = this.nodeForm.dependencyNodeIdList

      // 依赖的节点ID
      if (dependencyNodeIdList instanceof Array && dependencyNodeIdList.length > 0) {
        for (let i = 0; i < dependencyNodeIdList.length; i++) {
          const nodeId = dependencyNodeIdList[i]
          const node = this.dataFrom.nodes.find(item => item.id === nodeId)
          console.log('当前依赖的节点信息', node)
          const moduleId = node.moduleId
          dependencyNodes.push({
            nodeId: nodeId,
            moduleId: moduleId
          })
        }
      }

      // 保存节点
      const params = {
        id: this.nodeForm.id,
        moduleId: this.nodeForm.moduleId,
        type: this.nodeForm.type,
        name: this.nodeForm.name,
        topologyId: this.currentTopoId,
        attrList: this.nodeForm.attrList,
        dependencyNodes: dependencyNodes

      }
      if (!params.moduleId) {
        this.$message.success('所选模块不能为空！')
        return
      }

      console.log('保存节点: ', params)
      await API.saveNode(params)
      this.$message.success('保存成功！')
      this.nodeDrawer = false

      await this.getDetail()
    },

    async handleSave() {
      // 保存拓扑
      const params = {
        id: this.currentTopoId,
        name: this.dataFrom.name,
        describeInfo: this.dataFrom.describeInfo,
        isAutoPlacement: this.dataFrom.isAutoPlacement,
        helpUrl: this.dataFrom.helpUrl,
        attrList: this.dataFrom.attrList, // 属性
        notOversell: this.dataFrom.notOversell
      }
      console.log('保存内容', params)
      if (!params.name) {
        this.$message.error('名称不能为空！')
        return
      }

      const res = await API.saveTopolopy(params)
      console.log('保存结果', res)

      if (!this.currentTopoId) {
        window.location.href = '/topology'
      }

      this.$message.success('保存成功！')
    },
    async openNode() {
      // 添加节点
      this.nodeForm = { // 当前节点
        id: null,
        name: null, // 节点名
        moduleId: null,
        dependencyNodeIds: [],
        attrList: [],
        type: null, // 节点类型
        moduleInfo: null
      }
      await this.getModuleList()

      this.dependencyNodeItems = this.dataFrom.nodes
      this.nodeTitle = '新增节点'
      this.nodeDrawer = true
    },
    async getModuleList() {
      const res = await API.fetchModuleList()
      // console.log('获取模块列表', res)
      this.moduleList = res.data
    },
    handleCloseAllowUser(tag) {
      // 删除授权用户信息
      this.dataFrom.powerUserList.splice(this.dataFrom.powerUserList.findIndex(item => item.id === tag.id), 1)
    },
    async onNodeClick(nodeObject) {
      // 检查是否点击的是虚拟根节点
      if (nodeObject.isVirtualRoot || nodeObject.id.startsWith('virtual_root_')) {
        console.log('点击了虚拟根节点，不进行编辑操作')
        return
      }

      await this.getModuleList()
      // 点击拓扑节点
      console.log('点击节点:', nodeObject)
      const currentNodeId = nodeObject.id

      // 重置节点表单
      this.nodeForm = {
        id: null,
        name: null,
        moduleId: null,
        dependencyNodeIdList: [],
        attrList: [],
        type: null,
        moduleInfo: null
      }

      // 查找对应的原始节点数据，确保ID类型一致
      const node = this.dataFrom.nodes.find(item => String(item.id) === String(currentNodeId))
      if (!node) {
        console.warn('未找到对应的节点数据:', currentNodeId)
        return
      }

      // 设置节点表单数据
      this.nodeForm = { ...node }

      // 查找模块信息
      const module = this.moduleList.find(item => item.id === node.moduleId)
      this.nodeForm.moduleInfo = module || null

      console.log('当前节点详情', this.nodeForm)

      // 高亮相关连线
      this.hightNodeLine(nodeObject)

      // 设置依赖节点选项（排除当前节点）
      this.dependencyNodeItems = this.dataFrom.nodes.filter(item => String(item.id) !== String(currentNodeId))
      this.nodeTitle = '编辑节点'
      this.nodeDrawer = true
    },
    showSeeksGraph() {
      if (!this.currentTopoId) {
        return
      }
      if (!this.dataFrom.nodes || !this.dataFrom.nodeLines) {
        return
      }

      // 验证原始数据
      console.log('原始节点数据:', this.dataFrom.nodes)
      console.log('原始连线数据:', this.dataFrom.nodeLines)

      // 检查连线数据的字段结构
      if (this.dataFrom.nodeLines.length > 0) {
        console.log('连线数据字段:', Object.keys(this.dataFrom.nodeLines[0]))
        console.log('第一条连线示例:', this.dataFrom.nodeLines[0])
      }

      // 分析原始拓扑结构
      this.logTopologyStructure()

      // 创建优化的图形数据
      const optimizedGraphData = this.createOptimizedGraphData()

      console.log('优化后的拓扑数据', JSON.stringify(optimizedGraphData))

      if (!optimizedGraphData || !optimizedGraphData.nodes || !optimizedGraphData.lines) {
        console.log('数据非法')
        return
      }

      // 最终验证数据
      const isValid = this.validateGraphData(optimizedGraphData)
      if (!isValid) {
        console.error('图形数据验证失败')
        return
      }

      // 设置图形数据并启用树形布局
      console.log('开始设置图形数据到relation-graph组件')
      this.$refs.seeksRelationGraph.setJsonData(optimizedGraphData, (graphInstance) => {
        // 图表渲染完成后的回调
        console.log('关系图渲染完成，应用树形布局')

        // 检查渲染后的节点和连线
        const renderedNodes = graphInstance.getNodes()
        const renderedLinks = graphInstance.getLinks()
        console.log('渲染后的节点数量:', renderedNodes.length)
        console.log('渲染后的连线数量:', renderedLinks.length)

        // 检查虚拟根节点是否被渲染
        const virtualRootNode = renderedNodes.find(n => n.id.includes('virtual_root_'))
        console.log('虚拟根节点是否被渲染:', !!virtualRootNode)
        if (virtualRootNode) {
          console.log('虚拟根节点详情:', virtualRootNode)
        }

        // 检查虚拟连线是否被渲染
        const virtualLinks = renderedLinks.filter(l =>
          l.fromNode && l.fromNode.id && l.fromNode.id.includes('virtual_root_')
        )
        console.log('虚拟连线数量:', virtualLinks.length)
        console.log('虚拟连线详情:', virtualLinks)

        // 确保使用树形布局
        setTimeout(() => {
          // 强制刷新布局
          graphInstance.refresh()

          // 再次延迟确保布局完成后适应视图
          setTimeout(() => {
            graphInstance.zoomToFit()
            console.log('树形布局应用完成')
          }, 300)
        }, 200)
      })
    },

    validateGraphData(graphData) {
      // 验证图形数据格式
      if (!graphData.rootId || typeof graphData.rootId !== 'string') {
        console.error('rootId 必须是字符串')
        return false
      }

      // 验证节点数据
      for (const node of graphData.nodes) {
        if (!node.id || typeof node.id !== 'string') {
          console.error('节点ID必须是字符串:', node)
          return false
        }
        if (!node.text || typeof node.text !== 'string') {
          console.error('节点text必须是字符串:', node)
          return false
        }
      }

      // 验证连线数据
      for (const line of graphData.lines) {
        if (!line.from || typeof line.from !== 'string') {
          console.error('连线from必须是字符串:', line)
          return false
        }
        if (!line.to || typeof line.to !== 'string') {
          console.error('连线to必须是字符串:', line)
          return false
        }
      }

      console.log('图形数据验证通过')
      return true
    },

    detectLineFields() {
      // 自动检测连线数据的字段名
      if (!this.dataFrom.nodeLines || this.dataFrom.nodeLines.length === 0) {
        return { fromField: 'fromNodeId', toField: 'toNodeId' }
      }

      const firstLine = this.dataFrom.nodeLines[0]
      const fields = Object.keys(firstLine)

      // 可能的字段名组合
      const possibleFromFields = ['fromNodeId', 'from', 'fromId', 'sourceId', 'source']
      const possibleToFields = ['toNodeId', 'to', 'toId', 'targetId', 'target']

      const fromField = possibleFromFields.find(field => fields.includes(field)) || 'fromNodeId'
      const toField = possibleToFields.find(field => fields.includes(field)) || 'toNodeId'

      console.log(`检测到连线字段: from=${fromField}, to=${toField}`)
      return { fromField, toField }
    },

    logTopologyStructure() {
      // 打印拓扑结构的可视化信息
      console.log('=== 原始拓扑结构 ===')

      const { fromField, toField } = this.detectLineFields()

      this.dataFrom.nodes.forEach(node => {
        const children = this.dataFrom.nodeLines
          .filter(line => String(line[fromField]) === String(node.id))
          .map(line => {
            const childNode = this.dataFrom.nodes.find(n => String(n.id) === String(line[toField]))
            return childNode ? childNode.name : line[toField]
          })

        const parents = this.dataFrom.nodeLines
          .filter(line => String(line[toField]) === String(node.id))
          .map(line => {
            const parentNode = this.dataFrom.nodes.find(n => String(n.id) === String(line[fromField]))
            return parentNode ? parentNode.name : line[fromField]
          })

        console.log(`节点 ${node.name} (ID: ${node.id}):`)
        console.log(`  父节点: ${parents.length > 0 ? parents.join(', ') : '无（第一层节点）'}`)
        console.log(`  子节点: ${children.length > 0 ? children.join(', ') : '无'}`)
      })
    },

    createOptimizedGraphData() {
      try {
        // 创建虚拟根节点ID
        const virtualRootId = `virtual_root_${this.currentTopoId}`

        // 验证原始数据
        if (!this.dataFrom.nodes || !Array.isArray(this.dataFrom.nodes)) {
          console.error('节点数据无效')
          return null
        }

        if (!this.dataFrom.nodeLines || !Array.isArray(this.dataFrom.nodeLines)) {
          console.error('连线数据无效')
          return null
        }

        // 处理节点数据，确保ID为字符串
        const processedNodes = this.dataFrom.nodes.map(node => {
          if (!node || node.id === undefined || node.id === null) {
            console.warn('跳过无效节点:', node)
            return null
          }

          return {
            id: String(node.id), // 确保ID为字符串
            text: this.getNodeDisplayText(node),
            nodeShape: 0,
            width: 150,
            height: 60,
            color: this.getNodeColor(node),
            borderColor: this.getNodeBorderColor(node),
            fontColor: '#333333',
            fontSize: 14,
            // 添加节点的自定义属性
            data: {
              moduleId: node.moduleId,
              type: node.type,
              attrList: node.attrList || [],
              originalNode: node
            }
          }
        }).filter(node => node !== null) // 过滤掉无效节点

        // 创建虚拟根节点作为树的根
        const virtualRootNode = {
          id: virtualRootId,
          text: this.dataFrom.name || '拓扑根节点',
          nodeShape: 0,
          width: 1, // 最小宽度
          height: 1, // 最小高度
          color: 'rgba(0, 0, 0, 0)', // 完全透明
          borderColor: 'rgba(0, 0, 0, 0)', // 透明边框
          fontColor: 'rgba(0, 0, 0, 0)', // 透明字体
          fontSize: 16,
          isVirtualRoot: true
        }

        // 自动检测连线数据的字段名
        const { fromField, toField } = this.detectLineFields()

        // 过滤并保持原始连线数据不变，确保from和to为字符串
        const originalLines = this.dataFrom.nodeLines
          .filter(line => line[fromField] && line[toField]) // 过滤掉无效连线
          .map(line => ({
            from: String(line[fromField]), // 确保为字符串
            to: String(line[toField]), // 确保为字符串
            text: line.text || '',
            color: '#666666',
            lineWidth: 2,
            arrow: 'to',
            lineShape: 1 // 直线
          }))

        // 第一层节点判定：dependencyNodeIds 为空的节点
        const allNodeIds = new Set(this.dataFrom.nodes.map(node => String(node.id)))

        // 判断节点的依赖字段是否为空
        const isEmptyDependency = (node) => {
          // 检查 dependencyNodeIds 字段
          if (node.dependencyNodeIds) {
            if (typeof node.dependencyNodeIds === 'string') {
              // 字符串类型：去除空格后检查是否为空
              return node.dependencyNodeIds.trim() === ''
            } else if (Array.isArray(node.dependencyNodeIds)) {
              // 数组类型：检查是否为空数组或只包含空值
              return node.dependencyNodeIds.length === 0 ||
                     node.dependencyNodeIds.every(id => !id || String(id).trim() === '')
            }
            // 其他类型认为不为空
            return false
          }

          // 检查 dependencyNodeIdList 字段（备用字段）
          if (node.dependencyNodeIdList) {
            if (Array.isArray(node.dependencyNodeIdList)) {
              return node.dependencyNodeIdList.length === 0 ||
                     node.dependencyNodeIdList.every(id => !id || String(id).trim() === '')
            }
            return false
          }

          // 如果两个字段都不存在或都为 null/undefined，认为是第一层节点
          return true
        }

        // 第一层节点：dependencyNodeIds 为空的节点
        const firstLevelNodeIds = this.dataFrom.nodes
          .filter(node => isEmptyDependency(node))
          .map(node => String(node.id))

        // 收集所有被依赖的节点ID（用于调试）
        const dependentNodeIds = new Set()
        this.dataFrom.nodes.forEach(node => {
          if (!isEmptyDependency(node)) {
            let depIds = []

            // 处理 dependencyNodeIds 字段
            if (node.dependencyNodeIds) {
              if (typeof node.dependencyNodeIds === 'string') {
                depIds = node.dependencyNodeIds.split(',').map(id => id.trim()).filter(id => id)
              } else if (Array.isArray(node.dependencyNodeIds)) {
                depIds = node.dependencyNodeIds.filter(id => id && String(id).trim() !== '')
              }
            }

            // 处理 dependencyNodeIdList 字段
            if (node.dependencyNodeIdList && Array.isArray(node.dependencyNodeIdList)) {
              depIds = depIds.concat(node.dependencyNodeIdList.filter(id => id && String(id).trim() !== ''))
            }

            depIds.forEach(depId => {
              dependentNodeIds.add(String(depId))
            })
          }
        })

        console.log('使用新的第一层节点判定标准：dependencyNodeIds 为空的节点')
        console.log('最终确定的第一层节点:', firstLevelNodeIds)

        // 用于调试的子节点集合（有父节点的节点）
        const childNodeIds = new Set(
          this.dataFrom.nodeLines
            .filter(line => line[fromField] && line[toField]) // 过滤掉无效连线
            .map(line => String(line[toField]))
        )

        // 详细调试信息
        console.log('=== 节点依赖分析 ===')
        console.log(`使用字段: from=${fromField}, to=${toField}`)
        console.log('所有节点IDs:', [...allNodeIds])
        console.log('被依赖的节点IDs（有依赖关系的节点指向的节点）:', [...dependentNodeIds])
        console.log('第一层节点IDs（dependencyNodeIds为空）:', firstLevelNodeIds)
        console.log('在连线中作为子节点的IDs:', [...childNodeIds])
        console.log('原始连线数据:', this.dataFrom.nodeLines)
        console.log('过滤后的连线数据:', originalLines)

        // 验证第一层节点（基于dependencyNodeIds为空判定）
        console.log('=== 第一层节点验证 ===')
        console.log(`第一层节点数量: ${firstLevelNodeIds.length}`)

        // 显示每个节点的依赖关系详情
        console.log('=== 所有节点的依赖关系 ===')
        this.dataFrom.nodes.forEach(node => {
          const isFirstLevel = firstLevelNodeIds.includes(String(node.id))
          const depIds = node.dependencyNodeIds || ''
          const depIdList = node.dependencyNodeIdList || []
          const isEmpty = isEmptyDependency(node)

          console.log(`节点 ${node.name} (ID: ${node.id}):`)
          console.log(`  dependencyNodeIds: ${depIds || '无'}`)
          console.log(`  dependencyNodeIdList: ${Array.isArray(depIdList) ? depIdList.join(',') : depIdList || '无'}`)
          console.log(`  依赖字段是否为空: ${isEmpty}`)
          console.log(`  是第一层节点: ${isFirstLevel}`)
        })

        // 验证第一层节点
        firstLevelNodeIds.forEach(nodeId => {
          const node = this.dataFrom.nodes.find(n => String(n.id) === nodeId)
          const hasParentInLines = this.dataFrom.nodeLines.some(line =>
            line[fromField] && line[toField] && String(line[toField]) === nodeId
          )
          const isDependedOn = dependentNodeIds.has(nodeId)
          console.log(`第一层节点 ${node ? node.name : nodeId} (ID: ${nodeId}):`)
          console.log(`  被其他节点依赖: ${isDependedOn}`)
          console.log(`  在连线中有父节点: ${hasParentInLines}`)
        })

        // 只为第一层节点创建到虚拟根节点的连线
        console.log('=== 创建虚拟根节点连线 ===')
        console.log('虚拟根节点ID:', virtualRootId)
        console.log('第一层节点IDs:', firstLevelNodeIds)

        const virtualRootLines = firstLevelNodeIds.map(nodeId => {
          const line = {
            from: virtualRootId, // 虚拟根节点ID已经是字符串
            to: String(nodeId), // 确保为字符串
            text: '',
            color: 'rgba(0, 0, 0, 0)', // 完全透明
            lineWidth: 0,
            arrow: 'to',
            lineShape: 1, // 直线
            isVirtualLine: true
          }
          console.log(`创建虚拟连线: ${line.from} -> ${line.to}`)
          return line
        })

        console.log('虚拟根节点连线数量:', virtualRootLines.length)
        console.log('虚拟根节点连线详情:', virtualRootLines)

        // 合并所有节点和连线：虚拟根节点 + 原始节点，虚拟连线 + 原始连线
        const allNodes = [virtualRootNode, ...processedNodes]
        const allLines = [...virtualRootLines, ...originalLines]

        // 验证数据格式和结构
        console.log('=== 拓扑结构分析 ===')
        console.log('所有节点:', allNodeIds)
        console.log('被依赖的节点:', dependentNodeIds)
        console.log('第一层节点（dependencyNodeIds为空）:', firstLevelNodeIds)
        console.log('在连线中作为子节点的:', childNodeIds)
        console.log('原始连线数量:', originalLines.length)
        console.log('虚拟连线数量:', virtualRootLines.length)
        console.log('总连线数量:', allLines.length)

        console.log('验证节点数据:', allNodes.map(n => ({
          id: n.id,
          text: n.text,
          type: typeof n.id,
          isVirtual: n.isVirtualRoot || false
        })))
        console.log('验证连线数据:', allLines.map(l => ({
          from: l.from,
          to: l.to,
          fromType: typeof l.from,
          toType: typeof l.to,
          isVirtual: l.isVirtualLine || false
        })))

        const result = {
          rootId: virtualRootId,
          nodes: allNodes,
          lines: allLines
        }

        console.log('=== 最终返回的图形数据 ===')
        console.log('rootId:', result.rootId)
        console.log('节点数量:', result.nodes.length)
        console.log('连线数量:', result.lines.length)
        console.log('虚拟根节点是否存在:', result.nodes.some(n => n.id === virtualRootId))
        console.log('虚拟连线数量:', result.lines.filter(l => l.isVirtualLine).length)

        return result
      } catch (error) {
        console.error('创建图形数据时出错:', error)
        return null
      }
    },

    getNodeColor(node) {
      // 根据节点类型返回不同颜色
      if (node.type === 0) {
        return 'rgba(238, 178, 94, 0.9)' // base节点
      } else if (node.type === 1) {
        return 'rgba(94, 178, 238, 0.9)' // test节点
      } else if (node.type === 2) {
        return 'rgba(178, 238, 94, 0.9)' // 工具节点
      }
      return 'rgba(200, 200, 200, 0.9)' // 默认颜色
    },

    getNodeBorderColor(node) {
      // 根据节点类型返回不同边框颜色
      if (node.type === 0) {
        return '#D4A574' // base节点
      } else if (node.type === 1) {
        return '#74A5D4' // test节点
      } else if (node.type === 2) {
        return '#A5D474' // 工具节点
      }
      return '#CCCCCC' // 默认颜色
    },

    getNodeDisplayText(node) {
      // 简化节点显示文本，只显示节点名称，确保返回字符串
      const text = node.name || `节点${node.id}`
      return String(text)
    },
    getModuleTypeLabel(type) {
      // 根据模块类型值返回对应的中文标签
      const moduleType = MODULE_TYPES.find(item => item.value === type)
      return moduleType ? moduleType.label : type
    },
    getModuleLimitationLabel(type) {
      // 根据模块类型值返回对应的中文标签
      const limitationType = MODULE_MACHINE_LIMIT_TYPES.find(item => item.value === type)
      return limitationType ? limitationType.label : type
    },
    hightNodeLine(nodeObject) {
      // 高亮与选中节点相关的连线
      const allLinks = this.$refs.seeksRelationGraph.getLinks()

      // 还原所有连线样式
      allLinks.forEach(link => {
        link.relations.forEach(line => {
          if (line.data.orignColor) {
            line.color = line.data.orignColor
          } else {
            line.color = '#999999' // 默认颜色
          }
          if (line.data.orignFontColor) {
            line.fontColor = line.data.orignFontColor
          }
          if (line.data.orignLineWidth) {
            line.lineWidth = line.data.orignLineWidth
          } else {
            line.lineWidth = 2 // 默认宽度
          }
        })
      })

      // 高亮与选中节点相关的连线
      allLinks.filter(link => (link.fromNode === nodeObject || link.toNode === nodeObject)).forEach(link => {
        link.relations.forEach(line => {
          // 保存原始样式
          if (!line.data.orignColor) {
            line.data.orignColor = line.color
            line.data.orignFontColor = line.fontColor || line.color
            line.data.orignLineWidth = line.lineWidth || 2
          }
          // 设置高亮样式
          line.color = '#ff4444'
          line.fontColor = '#ff4444'
          line.lineWidth = 4
        })
      })

      // 强制更新视图
      this.$refs.seeksRelationGraph.getInstance().dataUpdated()
    },

    refreshGraphLayout() {
      // 刷新图形布局
      if (this.$refs.seeksRelationGraph) {
        const graphInstance = this.$refs.seeksRelationGraph.getInstance()
        if (graphInstance) {
          console.log('刷新树形布局')

          // 重新设置数据以确保树形布局正确应用
          const optimizedGraphData = this.createOptimizedGraphData()
          graphInstance.setJsonData(optimizedGraphData, () => {
            setTimeout(() => {
              graphInstance.refresh()
              setTimeout(() => {
                graphInstance.zoomToFit()
                console.log('树形布局刷新完成')
              }, 300)
            }, 200)
          })
        }
      }
    },

    fitGraphToView() {
      // 适应视图大小
      if (this.$refs.seeksRelationGraph) {
        const graphInstance = this.$refs.seeksRelationGraph.getInstance()
        if (graphInstance) {
          console.log('适应视图大小')
          graphInstance.zoomToFit()
        }
      }
    },

    testVirtualRoot() {
      // 测试虚拟根节点功能
      console.log('=== 测试虚拟根节点功能 ===')

      if (!this.dataFrom.nodes || this.dataFrom.nodes.length === 0) {
        console.error('没有节点数据')
        this.$message.error('没有节点数据')
        return
      }

      // 创建一个简单的测试数据
      const testData = {
        rootId: 'test_virtual_root',
        nodes: [
          {
            id: 'test_virtual_root',
            text: '测试虚拟根节点',
            nodeShape: 0,
            width: 200,
            height: 60,
            color: 'rgba(255, 0, 0, 0.8)',
            borderColor: '#FF0000',
            fontColor: '#ffffff',
            fontSize: 16
          },
          {
            id: 'test_node_1',
            text: '测试节点1',
            nodeShape: 0,
            width: 150,
            height: 60,
            color: 'rgba(0, 255, 0, 0.8)',
            borderColor: '#00FF00',
            fontColor: '#333333',
            fontSize: 14
          },
          {
            id: 'test_node_2',
            text: '测试节点2',
            nodeShape: 0,
            width: 150,
            height: 60,
            color: 'rgba(0, 0, 255, 0.8)',
            borderColor: '#0000FF',
            fontColor: '#ffffff',
            fontSize: 14
          }
        ],
        lines: [
          {
            from: 'test_virtual_root',
            to: 'test_node_1',
            text: '测试连线1',
            color: '#FF0000',
            lineWidth: 3,
            arrow: 'to',
            lineShape: 1
          },
          {
            from: 'test_virtual_root',
            to: 'test_node_2',
            text: '测试连线2',
            color: '#FF0000',
            lineWidth: 3,
            arrow: 'to',
            lineShape: 1
          }
        ]
      }

      console.log('设置测试数据:', testData)
      this.$refs.seeksRelationGraph.setJsonData(testData, (graphInstance) => {
        console.log('测试数据渲染完成')
        setTimeout(() => {
          graphInstance.zoomToFit()
        }, 200)
      })

      this.$message.success('已设置测试虚拟根节点数据')
    }

  }
}

</script>
<style scoped>
.task-container {
  padding: 20px;
}

.img-container{
  box-shadow: 0 2px 4px 0 rgba(0,0,0,.12), 0 0 6px 0 rgba(0,0,0,.04);
  width: 100%;
  height: 1000px;
}

::v-deep(.my-node-style) {
  padding: 5px 10px;
  border: 0px solid !important;
  /*margin: 10px;*/
}

::v-deep(.rel-easy-view) {
  background-color: #020202 !important;
}

::v-deep(.el-table--mini .el-table__cell) {
  padding: 0px 0 !important;
}

::v-deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>
