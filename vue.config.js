/* eslint-disable no-param-reassign */
const path = require('path')
const UglifyPlugin = require('uglifyjs-webpack-plugin')

const resolve = dir => path.join(__dirname, dir)

module.exports = {
  // 基本路径
  // publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  // 输出文件目录
  outputDir: 'dist',
  // eslint-loader 是否在保存的时候检查
  lintOnSave: false,
  runtimeCompiler: true, // 关键点在这
  // 调整内部的 webpack 配置。
  chainWebpack: (config) => {
    config.resolve.alias
      .set('@', resolve('src'))
      .set('assets', resolve('src/assets'))
      .set('components', resolve('src/components'))
      .set('public', resolve('public'))

    // 生产环境下，给文件名添加哈希值
    if (process.env.NODE_ENV === 'production') {
      config.output.filename('js/[name].[contenthash:8].js').end()
      config.output.chunkFilename('js/[name].[contenthash:8].js').end()
    }
  },
  // 生产环境是否生成 sourceMap 文件
  productionSourceMap: false,
  // css相关配置
  css: {
    // 是否使用css分离插件 ExtractTextPlugin
    // extract: true, // 注释css热更新生效
    // 开启 CSS source maps?
    sourceMap: false,
    // css预设器配置项
    loaderOptions: {
    },
    // 启用 CSS modules for all css / pre-processor files.
    requireModuleExtension: true,
    extract: {
      filename: 'css/[name].[contenthash:8].css',
      chunkFilename: 'css/[name].[contenthash:8].css'
    }
  },
  // webpack-dev-server 相关配置
  devServer: {
    /* 自动打开浏览器 */
    open: process.platform === 'darwin',
    host: 'test.xqp.jd.com',
    allowedHosts: [
      '.jd.com',
      '.jdwl.com',
      '.jdl.com'
    ],
    port: 80,
    https: false,
    hotOnly: false,
    /* 使用代理 */
    proxy: {
      '/api': {
        /* 目标代理服务器地址 */
        // target: 'http://127.0.0.1:8888/',
        target: 'http://xqtp-service.jd.com/',
        /* 允许跨域 */
        changeOrigin: true,
        ws: true

      }
    },
    before: () => {}
  },
  // 第三方插件配置
  pluginOptions: {}
}
